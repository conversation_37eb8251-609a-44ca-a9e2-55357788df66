import 'dart:convert';

class TasksResponseEntity {
  List<String>? deleteTaskIds;
  List<TaskDetail>? updateTasksDocuments;
  List<TaskDetail>? updateTasksForms;
  List<TaskMembersHelper>? updateTaskMembers;
  List<TaskDetail>? updateTasksTasks;
  List<TaskDetail>? updateTasksPhotos;
  List<TaskDetail>? addTasks;
  List<TaskDetail>? updateTasksSubmission;
  List<TaskDetail>? updateTasksSignatures;
  // Photo type processing fields
  List<PhotoTypeListModel>? updatePhototypes;
  List<PhotoTypeListModel>? addPhototypes;
  List<PhotoTypeDeleteListModel>? deletePhototypes;
  // Signature type processing fields
  List<SignatureTypeListModel>? updateSignatureTypes;
  List<SignatureTypeListModel>? addSignatureTypes;
  List<SignatureTypeDeleteListModel>? deleteSignatureTypes;
  // New field to capture uploaded task ids list
  List<String>? uploadTaskIds;

  TasksResponseEntity({
    this.deleteTaskIds,
    this.updateTasksDocuments,
    this.updateTasksForms,
    this.updateTaskMembers,
    this.updateTasksTasks,
    this.updateTasksPhotos,
    this.addTasks,
    this.updateTasksSubmission,
    this.updateTasksSignatures,
    this.uploadTaskIds,
  });

  factory TasksResponseEntity.fromRawJson(String str) =>
      TasksResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory TasksResponseEntity.fromJson(Map<String, dynamic> json) {
    // Handle possible nested 'data' wrapper returned by API
    final Map<String, dynamic> data =
        json.containsKey('data') && json['data'] is Map<String, dynamic>
            ? json['data'] as Map<String, dynamic>
            : json;

    return TasksResponseEntity(
      deleteTaskIds: data["delete_task_ids"] == null
          ? []
          : List<String>.from(
              data["delete_task_ids"]!.map((x) => x.toString())),
      updateTasksDocuments: data["update_tasks_documents"] == null
          ? []
          : List<TaskDetail>.from(data["update_tasks_documents"]!
              .map((x) => TaskDetail.fromJson(x))),
      updateTasksForms: data["update_tasks_forms"] == null
          ? []
          : List<TaskDetail>.from(
              data["update_tasks_forms"]!.map((x) => TaskDetail.fromJson(x))),
      updateTaskMembers: data["update_task_members"] == null
          ? []
          : List<TaskMembersHelper>.from(data["update_task_members"]!
              .map((x) => TaskMembersHelper.fromJson(x))),
      updateTasksTasks: data["update_tasks_tasks"] == null
          ? []
          : List<TaskDetail>.from(
              data["update_tasks_tasks"]!.map((x) => TaskDetail.fromJson(x))),
      updateTasksPhotos: data["update_tasks_photos"] == null
          ? []
          : List<TaskDetail>.from(
              data["update_tasks_photos"]!.map((x) => TaskDetail.fromJson(x))),
      addTasks: data["add_tasks"] == null
          ? []
          : List<TaskDetail>.from(
              data["add_tasks"]!.map((x) => TaskDetail.fromJson(x))),
      updateTasksSubmission: data["update_tasks_submission"] == null
          ? []
          : List<TaskDetail>.from(data["update_tasks_submission"]!
              .map((x) => TaskDetail.fromJson(x))),
      updateTasksSignatures: data["update_tasks_signatures"] == null
          ? []
          : List<TaskDetail>.from(data["update_tasks_signatures"]!
              .map((x) => TaskDetail.fromJson(x))),
      uploadTaskIds: data["upload_task_ids"] == null
          ? []
          : List<String>.from(
              data["upload_task_ids"]!.map((x) => x.toString())),
    );
  }

  Map<String, dynamic> toJson() => {
        "delete_task_ids": deleteTaskIds == null
            ? []
            : List<dynamic>.from(deleteTaskIds!.map((x) => x)),
        "update_tasks_documents": updateTasksDocuments == null
            ? []
            : List<dynamic>.from(updateTasksDocuments!.map((x) => x.toJson())),
        "update_tasks_forms": updateTasksForms == null
            ? []
            : List<dynamic>.from(updateTasksForms!.map((x) => x.toJson())),
        "update_task_members": updateTaskMembers == null
            ? []
            : List<dynamic>.from(updateTaskMembers!.map((x) => x.toJson())),
        "update_tasks_tasks": updateTasksTasks == null
            ? []
            : List<dynamic>.from(updateTasksTasks!.map((x) => x.toJson())),
        "update_tasks_photos": updateTasksPhotos == null
            ? []
            : List<dynamic>.from(updateTasksPhotos!.map((x) => x.toJson())),
        "add_tasks": addTasks == null
            ? []
            : List<dynamic>.from(addTasks!.map((x) => x.toJson())),
        "update_tasks_submission": updateTasksSubmission == null
            ? []
            : List<dynamic>.from(updateTasksSubmission!.map((x) => x.toJson())),
        "update_tasks_signatures": updateTasksSignatures == null
            ? []
            : List<dynamic>.from(updateTasksSignatures!.map((x) => x.toJson())),
        "upload_task_ids": uploadTaskIds == null
            ? []
            : List<dynamic>.from(uploadTaskIds!.map((x) => x)),
      };
}

class TaskDetail {
  num? taskId;
  num? projectId;
  num? scheduleId;
  bool? sentToPayroll;
  bool? showKm;
  num? storeId;
  String? client;
  num? clientId;
  String? clientLogoUrl;
  String? storeGroup;
  num? storeGroupId;
  String? storeName;
  String? storeEmail;
  num? minutes;
  num? budget;
  num? originalbudget;
  String? comment;
  num? claimableKms;
  num? flightDuration;
  num? pages;
  String? location;
  String? suburb;
  num? latitude;
  num? longitude;
  num? taskLatitude;
  num? taskLongitude;
  String? cycle;
  num? cycleId;
  bool? canDelete;
  DateTime? scheduledTimeStamp;
  DateTime? submissionTimeStamp;
  DateTime? expires;
  String? onTask;
  String? phone;
  DateTime? rangeStart;
  DateTime? rangeEnd;
  bool? reOpened;
  String? reOpenedReason;
  String? taskStatus;
  num? warehousejobId;
  String? connoteUrl;
  bool? posRequired;
  bool? isPosMandatory;
  String? posReceived;
  List<PhotoFolder>? photoFolder;
  List<SignatureFolder>? signatureFolder;
  List<Form>? forms;
  List<PosItem>? posItems;
  List<Document>? documents;
  List<Taskalert>? taskalerts;
  List<Taskmember>? taskmembers;
  DateTime? modifiedTimeStampDocuments;
  DateTime? modifiedTimeStampForms;
  DateTime? modifiedTimeStampMembers;
  DateTime? modifiedTimeStampTask;
  DateTime? modifiedTimeStampPhotos;
  DateTime? modifiedTimeStampSignatures;
  DateTime? modifiedTimeStampSignaturetypes;
  String? posSentTo;
  String? posSentToEmail;
  DateTime? modifiedTimeStampPhototypes;
  DateTime? taskCommencementTimeStamp;
  DateTime? taskStoppedTimeStamp;
  num? teamlead;
  List<FollowupTask>? followupTasks;
  List<Stocktake>? stocktake;
  String? taskNote;
  bool? disallowReschedule;
  num? photoResPerc;
  bool? liveImagesOnly;
  String? timeSchedule;
  num? scheduleTypeId;
  bool? showFollowupIconMulti;
  bool? followupSelectedMulti;
  num? regionId;
  bool? isOpen;
  num? taskCount;
  num? ctFormsTotalCnt;
  num? ctFormsCompletedCnt;
  String? preftime;
  bool? sendTo;
  List<ResumePauseItem>? resumePauseItems;
  num? kTotal;
  num? kCompleted;
  int? submissionState;
  bool? syncPending;

  TaskDetail({
    this.taskId,
    this.projectId,
    this.scheduleId,
    this.sentToPayroll,
    this.showKm,
    this.storeId,
    this.client,
    this.clientId,
    this.clientLogoUrl,
    this.storeGroup,
    this.storeGroupId,
    this.storeName,
    this.storeEmail,
    this.minutes,
    this.budget,
    this.originalbudget,
    this.comment,
    this.claimableKms,
    this.flightDuration,
    this.pages,
    this.location,
    this.suburb,
    this.latitude,
    this.longitude,
    this.taskLatitude,
    this.taskLongitude,
    this.cycle,
    this.cycleId,
    this.canDelete,
    this.scheduledTimeStamp,
    this.submissionTimeStamp,
    this.expires,
    this.onTask,
    this.phone,
    this.rangeStart,
    this.rangeEnd,
    this.reOpened,
    this.reOpenedReason,
    this.taskStatus,
    this.warehousejobId,
    this.connoteUrl,
    this.posRequired,
    this.isPosMandatory,
    this.posReceived,
    this.photoFolder,
    this.signatureFolder,
    this.forms,
    this.posItems,
    this.documents,
    this.taskalerts,
    this.taskmembers,
    this.modifiedTimeStampDocuments,
    this.modifiedTimeStampForms,
    this.modifiedTimeStampMembers,
    this.modifiedTimeStampTask,
    this.modifiedTimeStampPhotos,
    this.modifiedTimeStampSignatures,
    this.modifiedTimeStampSignaturetypes,
    this.posSentTo,
    this.posSentToEmail,
    this.modifiedTimeStampPhototypes,
    this.taskCommencementTimeStamp,
    this.taskStoppedTimeStamp,
    this.teamlead,
    this.followupTasks,
    this.stocktake,
    this.taskNote,
    this.disallowReschedule,
    this.photoResPerc,
    this.liveImagesOnly,
    this.timeSchedule,
    this.scheduleTypeId,
    this.showFollowupIconMulti,
    this.followupSelectedMulti,
    this.regionId,
    this.isOpen,
    this.taskCount,
    this.ctFormsTotalCnt,
    this.ctFormsCompletedCnt,
    this.preftime,
    this.sendTo,
    this.resumePauseItems,
    this.kTotal,
    this.kCompleted,
    this.submissionState,
    this.syncPending,
  });

  factory TaskDetail.fromRawJson(String str) =>
      TaskDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory TaskDetail.fromJson(Map<String, dynamic> json) => TaskDetail(
        taskId: json["task_id"],
        projectId: json["project_id"],
        scheduleId: json["schedule_id"],
        sentToPayroll: json["sent_to_payroll"],
        showKm: json["show_km"],
        storeId: json["store_id"],
        client: json["client"],
        clientId: json["client_id"],
        clientLogoUrl: json["client_logo_url"],
        storeGroup: json["store_group"],
        storeGroupId: json["store_group_id"],
        storeName: json["store_name"],
        storeEmail: json["store_email"],
        minutes: json["minutes"],
        budget: json["budget"],
        originalbudget: json["originalbudget"],
        comment: json["comment"],
        claimableKms: json["claimable_kms"],
        flightDuration: json["flight_duration"],
        pages: json["pages"],
        location: json["location"],
        suburb: json["suburb"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        taskLatitude: json["task_latitude"],
        taskLongitude: json["task_longitude"],
        cycle: json["cycle"],
        cycleId: json["cycle_id"],
        canDelete: json["can_delete"],
        scheduledTimeStamp: json["scheduled_time_stamp"] == null
            ? null
            : DateTime.parse(json["scheduled_time_stamp"]),
        submissionTimeStamp: json["submission_time_stamp"] == null
            ? null
            : DateTime.parse(json["submission_time_stamp"]),
        expires:
            json["expires"] == null ? null : DateTime.parse(json["expires"]),
        onTask: json["on_task"],
        phone: json["phone"],
        rangeStart: json["range_start"] == null
            ? null
            : DateTime.parse(json["range_start"]),
        rangeEnd: json["range_end"] == null
            ? null
            : DateTime.parse(json["range_end"]),
        reOpened: json["re_opened"],
        reOpenedReason: json["re_opened_reason"],
        taskStatus: json["task_status"],
        warehousejobId: json["warehousejob_id"],
        connoteUrl: json["connote_url"],
        posRequired: json["pos_required"],
        isPosMandatory: json["is_pos_mandatory"],
        posReceived: json["pos_received"],
        photoFolder: json["photo_folder"] == null
            ? []
            : List<PhotoFolder>.from(
                json["photo_folder"]!.map((x) => PhotoFolder.fromJson(x))),
        signatureFolder: json["signature_folder"] == null
            ? []
            : List<SignatureFolder>.from(json["signature_folder"]!
                .map((x) => SignatureFolder.fromJson(x))),
        forms: json["forms"] == null
            ? []
            : List<Form>.from(json["forms"]!.map((x) => Form.fromJson(x))),
        posItems: json["pos_items"] == null
            ? []
            : List<PosItem>.from(
                json["pos_items"]!.map((x) => PosItem.fromJson(x))),
        documents: json["documents"] == null
            ? []
            : List<Document>.from(
                json["documents"]!.map((x) => Document.fromJson(x))),
        taskalerts: json["taskalerts"] == null
            ? []
            : List<Taskalert>.from(
                json["taskalerts"]!.map((x) => Taskalert.fromJson(x))),
        taskmembers: json["taskmembers"] == null
            ? []
            : List<Taskmember>.from(
                json["taskmembers"]!.map((x) => Taskmember.fromJson(x))),
        modifiedTimeStampDocuments:
            json["modified_time_stamp_documents"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_documents"]),
        modifiedTimeStampForms: json["modified_time_stamp_forms"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_forms"]),
        modifiedTimeStampMembers: json["modified_time_stamp_members"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_members"]),
        modifiedTimeStampTask: json["modified_time_stamp_task"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_task"]),
        modifiedTimeStampPhotos: json["modified_time_stamp_photos"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_photos"]),
        modifiedTimeStampSignatures:
            json["modified_time_stamp_signatures"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signatures"]),
        modifiedTimeStampSignaturetypes:
            json["modified_time_stamp_signaturetypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signaturetypes"]),
        posSentTo: json["Pos_Sent_To"],
        posSentToEmail: json["Pos_Sent_To_Email"],
        modifiedTimeStampPhototypes:
            json["modified_time_stamp_phototypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototypes"]),
        taskCommencementTimeStamp: json["task_commencement_time_stamp"] == null
            ? null
            : DateTime.parse(json["task_commencement_time_stamp"]),
        taskStoppedTimeStamp: json["task_stopped_time_stamp"] == null
            ? null
            : DateTime.parse(json["task_stopped_time_stamp"]),
        teamlead: json["teamlead"],
        followupTasks: json["followup_tasks"] == null
            ? []
            : List<FollowupTask>.from(
                json["followup_tasks"]!.map((x) => FollowupTask.fromJson(x))),
        stocktake: json["stocktake"] == null
            ? []
            : List<Stocktake>.from(
                json["stocktake"]!.map((x) => Stocktake.fromJson(x))),
        taskNote: json["task_note"],
        disallowReschedule: json["disallow_reschedule"],
        photoResPerc: json["PhotoResPerc"],
        liveImagesOnly: json["live_images_only"],
        timeSchedule: json["time_schedule"],
        scheduleTypeId: json["ScheduleTypeID"],
        showFollowupIconMulti: json["show_followup_icon_multi"],
        followupSelectedMulti: json["followup_selected_multi"],
        regionId: json["region_id"],
        isOpen: json["is_open"],
        taskCount: json["task_count"],
        ctFormsTotalCnt: json["ct_forms_total_cnt"],
        ctFormsCompletedCnt: json["ct_forms_completed_cnt"],
        preftime: json["preftime"],
        sendTo: json["send_to"],
        resumePauseItems: json["resume_pause_items"] == null
            ? []
            : List<ResumePauseItem>.from(json["resume_pause_items"]!
                .map((x) => ResumePauseItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "project_id": projectId,
        "schedule_id": scheduleId,
        "sent_to_payroll": sentToPayroll,
        "show_km": showKm,
        "store_id": storeId,
        "client": client,
        "client_id": clientId,
        "client_logo_url": clientLogoUrl,
        "store_group": storeGroup,
        "store_group_id": storeGroupId,
        "store_name": storeName,
        "store_email": storeEmail,
        "minutes": minutes,
        "budget": budget,
        "originalbudget": originalbudget,
        "comment": comment,
        "claimable_kms": claimableKms,
        "flight_duration": flightDuration,
        "pages": pages,
        "location": location,
        "suburb": suburb,
        "latitude": latitude,
        "longitude": longitude,
        "task_latitude": taskLatitude,
        "task_longitude": taskLongitude,
        "cycle": cycle,
        "cycle_id": cycleId,
        "can_delete": canDelete,
        "scheduled_time_stamp": scheduledTimeStamp?.toIso8601String(),
        "submission_time_stamp": submissionTimeStamp?.toIso8601String(),
        "expires": expires?.toIso8601String(),
        "on_task": onTask,
        "phone": phone,
        "range_start": rangeStart?.toIso8601String(),
        "range_end": rangeEnd?.toIso8601String(),
        "re_opened": reOpened,
        "re_opened_reason": reOpenedReason,
        "task_status": taskStatus,
        "warehousejob_id": warehousejobId,
        "connote_url": connoteUrl,
        "pos_required": posRequired,
        "is_pos_mandatory": isPosMandatory,
        "pos_received": posReceived,
        "photo_folder": photoFolder == null
            ? []
            : List<dynamic>.from(photoFolder!.map((x) => x.toJson())),
        "signature_folder": signatureFolder == null
            ? []
            : List<dynamic>.from(signatureFolder!.map((x) => x.toJson())),
        "forms": forms == null
            ? []
            : List<dynamic>.from(forms!.map((x) => x.toJson())),
        "pos_items": posItems == null
            ? []
            : List<dynamic>.from(posItems!.map((x) => x.toJson())),
        "documents": documents == null
            ? []
            : List<dynamic>.from(documents!.map((x) => x.toJson())),
        "taskalerts": taskalerts == null
            ? []
            : List<dynamic>.from(taskalerts!.map((x) => x.toJson())),
        "taskmembers": taskmembers == null
            ? []
            : List<dynamic>.from(taskmembers!.map((x) => x.toJson())),
        "modified_time_stamp_documents":
            modifiedTimeStampDocuments?.toIso8601String(),
        "modified_time_stamp_forms": modifiedTimeStampForms?.toIso8601String(),
        "modified_time_stamp_members":
            modifiedTimeStampMembers?.toIso8601String(),
        "modified_time_stamp_task": modifiedTimeStampTask?.toIso8601String(),
        "modified_time_stamp_photos":
            modifiedTimeStampPhotos?.toIso8601String(),
        "modified_time_stamp_signatures":
            modifiedTimeStampSignatures?.toIso8601String(),
        "modified_time_stamp_signaturetypes":
            modifiedTimeStampSignaturetypes?.toIso8601String(),
        "Pos_Sent_To": posSentTo,
        "Pos_Sent_To_Email": posSentToEmail,
        "modified_time_stamp_phototypes":
            modifiedTimeStampPhototypes?.toIso8601String(),
        "task_commencement_time_stamp":
            taskCommencementTimeStamp?.toIso8601String(),
        "task_stopped_time_stamp": taskStoppedTimeStamp?.toIso8601String(),
        "teamlead": teamlead,
        "followup_tasks": followupTasks == null
            ? []
            : List<dynamic>.from(followupTasks!.map((x) => x.toJson())),
        "stocktake": stocktake == null
            ? []
            : List<dynamic>.from(stocktake!.map((x) => x.toJson())),
        "task_note": taskNote,
        "disallow_reschedule": disallowReschedule,
        "PhotoResPerc": photoResPerc,
        "live_images_only": liveImagesOnly,
        "time_schedule": timeSchedule,
        "ScheduleTypeID": scheduleTypeId,
        "show_followup_icon_multi": showFollowupIconMulti,
        "followup_selected_multi": followupSelectedMulti,
        "region_id": regionId,
        "is_open": isOpen,
        "task_count": taskCount,
        "ct_forms_total_cnt": ctFormsTotalCnt,
        "ct_forms_completed_cnt": ctFormsCompletedCnt,
        "preftime": preftime,
        "send_to": sendTo,
        "sync_pending": syncPending,
        "resume_pause_items": resumePauseItems == null
            ? []
            : List<dynamic>.from(resumePauseItems!.map((x) => x.toJson())),
      };
}

class Document {
  num? projectId;
  num? documentId;
  num? documentTypeId;
  String? documentName;
  String? documentIconLink;
  List<FileElement>? files;
  DateTime? modifiedTimeStampDocument;

  Document({
    this.projectId,
    this.documentId,
    this.documentTypeId,
    this.documentName,
    this.documentIconLink,
    this.files,
    this.modifiedTimeStampDocument,
  });

  factory Document.fromRawJson(String str) =>
      Document.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Document.fromJson(Map<String, dynamic> json) => Document(
        projectId: json["project_id"],
        documentId: json["document_id"],
        documentTypeId: json["document_type_id"],
        documentName: json["document_name"],
        documentIconLink: json["document_icon_link"],
        files: json["files"] == null
            ? []
            : List<FileElement>.from(
                json["files"]!.map((x) => FileElement.fromJson(x))),
        modifiedTimeStampDocument: json["modified_time_stamp_document"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_document"]),
      );

  Map<String, dynamic> toJson() => {
        "project_id": projectId,
        "document_id": documentId,
        "document_type_id": documentTypeId,
        "document_name": documentName,
        "document_icon_link": documentIconLink,
        "files": files == null
            ? []
            : List<dynamic>.from(files!.map((x) => x.toJson())),
        "modified_time_stamp_document":
            modifiedTimeStampDocument?.toIso8601String(),
      };
}

class FileElement {
  num? documentId;
  num? projectId;
  String? documentFileLink;
  num? fileId;
  DateTime? modifiedTimeStampFile;

  FileElement({
    this.documentId,
    this.projectId,
    this.documentFileLink,
    this.fileId,
    this.modifiedTimeStampFile,
  });

  factory FileElement.fromRawJson(String str) =>
      FileElement.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FileElement.fromJson(Map<String, dynamic> json) => FileElement(
        documentId: json["document_id"],
        projectId: json["project_id"],
        documentFileLink: json["document_file_link"],
        fileId: json["file_id"],
        modifiedTimeStampFile: json["modified_time_stamp_file"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_file"]),
      );

  Map<String, dynamic> toJson() => {
        "document_id": documentId,
        "project_id": projectId,
        "document_file_link": documentFileLink,
        "file_id": fileId,
        "modified_time_stamp_file": modifiedTimeStampFile?.toIso8601String(),
      };
}

class FollowupTask {
  bool? showFollowupIcon;
  bool? followupSelected;
  DateTime? selectedVisitDate;
  num? selectedFollowupTypeId;
  num? selectedFollowupItemId;
  num? selectedBudget;
  String? selectedFollowupType;
  String? selectedFollowupItem;
  String? selectedScheduleNote;
  num? followupNumber;

  FollowupTask({
    this.showFollowupIcon,
    this.followupSelected,
    this.selectedVisitDate,
    this.selectedFollowupTypeId,
    this.selectedFollowupItemId,
    this.selectedBudget,
    this.selectedFollowupType,
    this.selectedFollowupItem,
    this.selectedScheduleNote,
    this.followupNumber,
  });

  factory FollowupTask.fromRawJson(String str) =>
      FollowupTask.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FollowupTask.fromJson(Map<String, dynamic> json) => FollowupTask(
        showFollowupIcon: json["show_followup_icon"],
        followupSelected: json["followup_selected"],
        selectedVisitDate: json["selected_visit_date"] == null
            ? null
            : DateTime.parse(json["selected_visit_date"]),
        selectedFollowupTypeId: json["selected_followup_type_id"],
        selectedFollowupItemId: json["selected_followup_item_id"],
        selectedBudget: json["selected_budget"],
        selectedFollowupType: json["selected_followup_type"],
        selectedFollowupItem: json["selected_followup_item"],
        selectedScheduleNote: json["selected_schedule_note"],
        followupNumber: json["followup_number"],
      );

  Map<String, dynamic> toJson() => {
        "show_followup_icon": showFollowupIcon,
        "followup_selected": followupSelected,
        "selected_visit_date": selectedVisitDate?.toIso8601String(),
        "selected_followup_type_id": selectedFollowupTypeId,
        "selected_followup_item_id": selectedFollowupItemId,
        "selected_budget": selectedBudget,
        "selected_followup_type": selectedFollowupType,
        "selected_followup_item": selectedFollowupItem,
        "selected_schedule_note": selectedScheduleNote,
        "followup_number": followupNumber,
      };
}

class Form {
  num? formId;
  num? formInstanceId;
  String? formName;
  String? briefUrl;
  List<Question>? questions;
  List<QuestionAnswer>? questionAnswers;
  DateTime? modifiedTimeStampForm;
  DateTime? dateStart;
  bool? isVisionForm;
  String? visionFormUrl;
  bool? isMandatory;
  bool? formPreview;
  num? formTypeId;
  bool? formCompleted;
  bool? formAllowForward;
  bool? formShowPrice;
  num? minQty;
  bool? showQuestions;
  int? kTotal;
  int? kCompleted;

  Form({
    this.formId,
    this.formInstanceId,
    this.formName,
    this.briefUrl,
    this.questions,
    this.questionAnswers,
    this.modifiedTimeStampForm,
    this.dateStart,
    this.isVisionForm,
    this.visionFormUrl,
    this.isMandatory,
    this.formPreview,
    this.formTypeId,
    this.formCompleted,
    this.formAllowForward,
    this.formShowPrice,
    this.minQty,
    this.showQuestions,
    this.kTotal,
    this.kCompleted,
  });

  factory Form.fromRawJson(String str) => Form.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Form.fromJson(Map<String, dynamic> json) => Form(
        formId: json["form_id"],
        formInstanceId: json["form_instance_id"],
        formName: json["form_name"],
        briefUrl: json["brief_url"],
        questions: json["questions"] == null
            ? []
            : List<Question>.from(
                json["questions"]!.map((x) => Question.fromJson(x))),
        questionAnswers: json["question_answers"] == null
            ? []
            : List<QuestionAnswer>.from(json["question_answers"]!
                .map((x) => QuestionAnswer.fromJson(x))),
        modifiedTimeStampForm: json["modified_time_stamp_form"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_form"]),
        dateStart: json["date_start"] == null
            ? null
            : DateTime.parse(json["date_start"]),
        isVisionForm: json["is_vision_form"],
        visionFormUrl: json["vision_form_url"],
        isMandatory: json["is_mandatory"],
        formPreview: json["form_preview"],
        formTypeId: json["form_type_id"],
        formCompleted: json["form_completed"],
        formAllowForward: json["form_allowForward"],
        formShowPrice: json["form_show_price"],
        minQty: json["min_qty"],
        showQuestions: json["show_questions"],
      );

  Map<String, dynamic> toJson() => {
        "form_id": formId,
        "form_instance_id": formInstanceId,
        "form_name": formName,
        "brief_url": briefUrl,
        "questions": questions == null
            ? []
            : List<dynamic>.from(questions!.map((x) => x.toJson())),
        "question_answers": questionAnswers == null
            ? []
            : List<dynamic>.from(questionAnswers!.map((x) => x.toJson())),
        "modified_time_stamp_form": modifiedTimeStampForm?.toIso8601String(),
        "date_start": dateStart?.toIso8601String(),
        "is_vision_form": isVisionForm,
        "vision_form_url": visionFormUrl,
        "is_mandatory": isMandatory,
        "form_preview": formPreview,
        "form_type_id": formTypeId,
        "form_completed": formCompleted,
        "form_allowForward": formAllowForward,
        "form_show_price": formShowPrice,
        "min_qty": minQty,
        "show_questions": showQuestions,
      };
}

class QuestionAnswer {
  num? taskId;
  num? formId;
  num? questionId;
  num? questionpartId;
  bool? flip;
  String? questionPartMultiId;
  num? measurementId;
  num? measurementTypeId;
  num? measurementOptionId;
  String? measurementOptionIds;
  String? measurementTextResult;
  bool? isComment;
  num? commentTypeId;

  QuestionAnswer({
    this.taskId,
    this.formId,
    this.questionId,
    this.questionpartId,
    this.flip,
    this.questionPartMultiId,
    this.measurementId,
    this.measurementTypeId,
    this.measurementOptionId,
    this.measurementOptionIds,
    this.measurementTextResult,
    this.isComment,
    this.commentTypeId,
  });

  factory QuestionAnswer.fromRawJson(String str) =>
      QuestionAnswer.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory QuestionAnswer.fromJson(Map<String, dynamic> json) => QuestionAnswer(
        taskId: json["task_id"],
        formId: json["form_id"],
        questionId: json["question_id"],
        questionpartId: json["questionpart_id"],
        flip: json["flip"],
        questionPartMultiId: json["question_part_multi_id"],
        measurementId: json["measurement_id"],
        measurementTypeId: json["measurement_type_id"],
        measurementOptionId: json["measurement_option_id"],
        measurementOptionIds: json["measurement_option_ids"],
        measurementTextResult: json["measurement_text_result"],
        isComment: json["is_comment"],
        commentTypeId: json["comment_type_id"],
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "form_id": formId,
        "question_id": questionId,
        "questionpart_id": questionpartId,
        "flip": flip,
        "question_part_multi_id": questionPartMultiId,
        "measurement_id": measurementId,
        "measurement_type_id": measurementTypeId,
        "measurement_option_id": measurementOptionId,
        "measurement_option_ids": measurementOptionIds,
        "measurement_text_result": measurementTextResult,
        "is_comment": isComment,
        "comment_type_id": commentTypeId,
      };
}

class Question {
  num? questionId;
  num? questionOrderId;
  String? questionDescription;
  bool? isComment;
  bool? isCommentMandatory;
  bool? showQuestions;
  bool? hasSignature;
  bool? isSignatureMandatory;
  String? signatureUrl;
  String? photoUrl;
  String? questionBrief;
  List<QuestionPart>? questionParts;
  List<Measurement>? measurements;
  List<QuestionCondition>? questionConditions;
  List<CommentType>? commentTypes;
  DateTime? modifiedTimeStampQuestion;
  bool? targetByCycle;
  bool? targetByGroup;
  bool? targetByCompany;
  bool? targetByRegion;
  bool? targetByBudget;
  bool? isMll;
  List<PhotoTagsT>? photoTagsTwo;
  List<PhotoTagsT>? photoTagsThree;
  bool? isMulti;
  num? multiMeasurementId;
  bool? isMultiOneAnswer;
  bool? flip;
  num? questionTypeId;

  Question({
    this.questionId,
    this.questionOrderId,
    this.questionDescription,
    this.isComment,
    this.isCommentMandatory,
    this.showQuestions,
    this.hasSignature,
    this.isSignatureMandatory,
    this.signatureUrl,
    this.photoUrl,
    this.questionBrief,
    this.questionParts,
    this.measurements,
    this.questionConditions,
    this.commentTypes,
    this.modifiedTimeStampQuestion,
    this.targetByCycle,
    this.targetByGroup,
    this.targetByCompany,
    this.targetByRegion,
    this.targetByBudget,
    this.isMll,
    this.photoTagsTwo,
    this.photoTagsThree,
    this.isMulti,
    this.multiMeasurementId,
    this.isMultiOneAnswer,
    this.flip,
    this.questionTypeId,
  });

  factory Question.fromRawJson(String str) =>
      Question.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        questionId: json["question_id"],
        questionOrderId: json["question_order_id"],
        questionDescription: json["question_description"],
        isComment: json["is_comment"],
        isCommentMandatory: json["is_comment_mandatory"],
        showQuestions: json["show_questions"],
        hasSignature: json["has_signature"],
        isSignatureMandatory: json["is_signature_mandatory"],
        signatureUrl: json["signature_url"],
        photoUrl: json["photo_url"],
        questionBrief: json["question_brief"],
        questionParts: json["question_parts"] == null
            ? []
            : List<QuestionPart>.from(
                json["question_parts"]!.map((x) => QuestionPart.fromJson(x))),
        measurements: json["measurements"] == null
            ? []
            : List<Measurement>.from(
                json["measurements"]!.map((x) => Measurement.fromJson(x))),
        questionConditions: json["question_conditions"] == null
            ? []
            : List<QuestionCondition>.from(json["question_conditions"]!
                .map((x) => QuestionCondition.fromJson(x))),
        commentTypes: json["comment_types"] == null
            ? []
            : List<CommentType>.from(
                json["comment_types"]!.map((x) => CommentType.fromJson(x))),
        modifiedTimeStampQuestion: json["modified_time_stamp_question"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_question"]),
        targetByCycle: json["targetByCycle"],
        targetByGroup: json["targetByGroup"],
        targetByCompany: json["targetByCompany"],
        targetByRegion: json["targetByRegion"],
        targetByBudget: json["targetByBudget"],
        isMll: json["is_mll"],
        photoTagsTwo: json["photo_tags_two"] == null
            ? []
            : List<PhotoTagsT>.from(
                json["photo_tags_two"]!.map((x) => PhotoTagsT.fromJson(x))),
        photoTagsThree: json["photo_tags_three"] == null
            ? []
            : List<PhotoTagsT>.from(
                json["photo_tags_three"]!.map((x) => PhotoTagsT.fromJson(x))),
        isMulti: json["is_multi"],
        multiMeasurementId: json["multi_measurement_id"],
        isMultiOneAnswer: json["is_multi_one_answer"],
        flip: json["flip"],
        questionTypeId: json["question_type_id"],
      );

  Map<String, dynamic> toJson() => {
        "question_id": questionId,
        "question_order_id": questionOrderId,
        "question_description": questionDescription,
        "is_comment": isComment,
        "is_comment_mandatory": isCommentMandatory,
        "show_questions": showQuestions,
        "has_signature": hasSignature,
        "is_signature_mandatory": isSignatureMandatory,
        "signature_url": signatureUrl,
        "photo_url": photoUrl,
        "question_brief": questionBrief,
        "question_parts": questionParts == null
            ? []
            : List<dynamic>.from(questionParts!.map((x) => x.toJson())),
        "measurements": measurements == null
            ? []
            : List<dynamic>.from(measurements!.map((x) => x.toJson())),
        "question_conditions": questionConditions == null
            ? []
            : List<dynamic>.from(questionConditions!.map((x) => x.toJson())),
        "comment_types": commentTypes == null
            ? []
            : List<dynamic>.from(commentTypes!.map((x) => x.toJson())),
        "modified_time_stamp_question":
            modifiedTimeStampQuestion?.toIso8601String(),
        "targetByCycle": targetByCycle,
        "targetByGroup": targetByGroup,
        "targetByCompany": targetByCompany,
        "targetByRegion": targetByRegion,
        "targetByBudget": targetByBudget,
        "is_mll": isMll,
        "photo_tags_two": photoTagsTwo == null
            ? []
            : List<dynamic>.from(photoTagsTwo!.map((x) => x.toJson())),
        "photo_tags_three": photoTagsThree == null
            ? []
            : List<dynamic>.from(photoTagsThree!.map((x) => x.toJson())),
        "is_multi": isMulti,
        "multi_measurement_id": multiMeasurementId,
        "is_multi_one_answer": isMultiOneAnswer,
        "flip": flip,
        "question_type_id": questionTypeId,
      };
}

class CommentType {
  num? commentTypeId;
  String? commentType;

  CommentType({
    this.commentTypeId,
    this.commentType,
  });

  factory CommentType.fromRawJson(String str) =>
      CommentType.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CommentType.fromJson(Map<String, dynamic> json) => CommentType(
        commentTypeId: json["comment_type_id"],
        commentType: json["comment_type"],
      );

  Map<String, dynamic> toJson() => {
        "comment_type_id": commentTypeId,
        "comment_type": commentType,
      };
}

class Measurement {
  num? measurementId;
  num? measurementTypeId;
  String? measurementDescription;
  String? measurementTypeName;
  String? defaultAction;
  List<MeasurementOption>? measurementOptions;
  List<MeasurementCondition>? measurementConditions;
  List<MeasurementCondition>? measurementConditionsMultiple;
  List<MeasurementValidation>? measurementValidations;
  String? measurementDefaultsResult;
  DateTime? modifiedTimeStampMeasurement;
  DateTime? modifiedTimeStampMeasurementDefaultsResult;
  num? measurementOrderId;
  num? mandatoryPhototypesCount;
  num? optionalPhototypesCount;
  String? measurementImage;
  num? companyid;
  List<MeasurementPhototypesDeprecated>? measurementPhototypesDeprecated;
  DateTime? modifiedTimeStampMeasurementvalidation;
  num? validationTypeId;
  bool? required;
  String? rangeValidation;
  String? expressionValidation;
  String? errorMessage;
  DateTime? modifiedTimeStampMeasurementdefault;

  Measurement({
    this.measurementId,
    this.measurementTypeId,
    this.measurementDescription,
    this.measurementTypeName,
    this.defaultAction,
    this.measurementOptions,
    this.measurementConditions,
    this.measurementConditionsMultiple,
    this.measurementValidations,
    this.measurementDefaultsResult,
    this.modifiedTimeStampMeasurement,
    this.modifiedTimeStampMeasurementDefaultsResult,
    this.measurementOrderId,
    this.mandatoryPhototypesCount,
    this.optionalPhototypesCount,
    this.measurementImage,
    this.companyid,
    this.measurementPhototypesDeprecated,
    this.modifiedTimeStampMeasurementvalidation,
    this.validationTypeId,
    this.required,
    this.rangeValidation,
    this.expressionValidation,
    this.errorMessage,
    this.modifiedTimeStampMeasurementdefault,
  });

  factory Measurement.fromRawJson(String str) =>
      Measurement.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Measurement.fromJson(Map<String, dynamic> json) => Measurement(
        measurementId: json["measurement_id"],
        measurementTypeId: json["measurement_type_id"],
        measurementDescription: json["measurement_description"],
        measurementTypeName: json["measurement_type_name"],
        defaultAction: json["default_action"],
        measurementOptions: json["measurement_options"] == null
            ? []
            : List<MeasurementOption>.from(json["measurement_options"]!
                .map((x) => MeasurementOption.fromJson(x))),
        measurementConditions: json["measurement_conditions"] == null
            ? []
            : List<MeasurementCondition>.from(json["measurement_conditions"]!
                .map((x) => MeasurementCondition.fromJson(x))),
        measurementConditionsMultiple:
            json["measurement_conditions_multiple"] == null
                ? []
                : List<MeasurementCondition>.from(
                    json["measurement_conditions_multiple"]!
                        .map((x) => MeasurementCondition.fromJson(x))),
        measurementValidations: json["measurement_validations"] == null
            ? []
            : List<MeasurementValidation>.from(json["measurement_validations"]!
                .map((x) => MeasurementValidation.fromJson(x))),
        measurementDefaultsResult: json["measurement_defaults_result"],
        modifiedTimeStampMeasurement:
            json["modified_time_stamp_measurement"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_measurement"]),
        modifiedTimeStampMeasurementDefaultsResult:
            json["modified_time_stamp_measurement_defaults_result"] == null
                ? null
                : DateTime.parse(
                    json["modified_time_stamp_measurement_defaults_result"]),
        measurementOrderId: json["measurement_order_id"],
        mandatoryPhototypesCount: json["mandatory_phototypes_count"],
        optionalPhototypesCount: json["optional_phototypes_count"],
        measurementImage: json["measurement_image"],
        companyid: json["companyid"],
        measurementPhototypesDeprecated:
            json["measurement_phototypes_deprecated"] == null
                ? []
                : List<MeasurementPhototypesDeprecated>.from(
                    json["measurement_phototypes_deprecated"]!.map(
                        (x) => MeasurementPhototypesDeprecated.fromJson(x))),
        modifiedTimeStampMeasurementvalidation:
            json["modified_time_stamp_measurementvalidation"] == null
                ? null
                : DateTime.parse(
                    json["modified_time_stamp_measurementvalidation"]),
        validationTypeId: json["validation_type_id"],
        required: json["required"],
        rangeValidation: json["range_validation"],
        expressionValidation: json["expression_validation"],
        errorMessage: json["error_message"],
        modifiedTimeStampMeasurementdefault:
            json["modified_time_stamp_measurementdefault"] == null
                ? null
                : DateTime.parse(
                    json["modified_time_stamp_measurementdefault"]),
      );

  Map<String, dynamic> toJson() => {
        "measurement_id": measurementId,
        "measurement_type_id": measurementTypeId,
        "measurement_description": measurementDescription,
        "measurement_type_name": measurementTypeName,
        "default_action": defaultAction,
        "measurement_options": measurementOptions == null
            ? []
            : List<dynamic>.from(measurementOptions!.map((x) => x.toJson())),
        "measurement_conditions": measurementConditions == null
            ? []
            : List<dynamic>.from(measurementConditions!.map((x) => x.toJson())),
        "measurement_conditions_multiple": measurementConditionsMultiple == null
            ? []
            : List<dynamic>.from(
                measurementConditionsMultiple!.map((x) => x.toJson())),
        "measurement_validations": measurementValidations == null
            ? []
            : List<dynamic>.from(
                measurementValidations!.map((x) => x.toJson())),
        "measurement_defaults_result": measurementDefaultsResult,
        "modified_time_stamp_measurement":
            modifiedTimeStampMeasurement?.toIso8601String(),
        "modified_time_stamp_measurement_defaults_result":
            modifiedTimeStampMeasurementDefaultsResult?.toIso8601String(),
        "measurement_order_id": measurementOrderId,
        "mandatory_phototypes_count": mandatoryPhototypesCount,
        "optional_phototypes_count": optionalPhototypesCount,
        "measurement_image": measurementImage,
        "companyid": companyid,
        "measurement_phototypes_deprecated":
            measurementPhototypesDeprecated == null
                ? []
                : List<dynamic>.from(
                    measurementPhototypesDeprecated!.map((x) => x.toJson())),
        "modified_time_stamp_measurementvalidation":
            modifiedTimeStampMeasurementvalidation?.toIso8601String(),
        "validation_type_id": validationTypeId,
        "required": required,
        "range_validation": rangeValidation,
        "expression_validation": expressionValidation,
        "error_message": errorMessage,
        "modified_time_stamp_measurementdefault":
            modifiedTimeStampMeasurementdefault?.toIso8601String(),
      };
}

class MeasurementCondition {
  num? measurementId;
  num? measurementOptionId;
  num? actionMeasurementId;
  String? action;
  DateTime? modifiedTimeStampMeasurementconidtion;

  MeasurementCondition({
    this.measurementId,
    this.measurementOptionId,
    this.actionMeasurementId,
    this.action,
    this.modifiedTimeStampMeasurementconidtion,
  });

  factory MeasurementCondition.fromRawJson(String str) =>
      MeasurementCondition.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MeasurementCondition.fromJson(Map<String, dynamic> json) =>
      MeasurementCondition(
        measurementId: json["measurement_id"],
        measurementOptionId: json["measurement_option_id"],
        actionMeasurementId: json["action_measurement_id"],
        action: json["action"],
        modifiedTimeStampMeasurementconidtion:
            json["modified_time_stamp_measurementconidtion"] == null
                ? null
                : DateTime.parse(
                    json["modified_time_stamp_measurementconidtion"]),
      );

  Map<String, dynamic> toJson() => {
        "measurement_id": measurementId,
        "measurement_option_id": measurementOptionId,
        "action_measurement_id": actionMeasurementId,
        "action": action,
        "modified_time_stamp_measurementconidtion":
            modifiedTimeStampMeasurementconidtion?.toIso8601String(),
      };
}

class MeasurementOption {
  num? measurementId;
  num? measurementOptionId;
  String? measurementOptionDescription;
  DateTime? modifiedTimeStampMeasurementoption;
  num? budgetOffset;
  num? budgetOffsetType;
  bool? isAnswer;

  MeasurementOption({
    this.measurementId,
    this.measurementOptionId,
    this.measurementOptionDescription,
    this.modifiedTimeStampMeasurementoption,
    this.budgetOffset,
    this.budgetOffsetType,
    this.isAnswer,
  });

  factory MeasurementOption.fromRawJson(String str) =>
      MeasurementOption.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MeasurementOption.fromJson(Map<String, dynamic> json) =>
      MeasurementOption(
        measurementId: json["measurement_id"],
        measurementOptionId: json["measurement_option_id"],
        measurementOptionDescription: json["measurement_option_description"],
        modifiedTimeStampMeasurementoption:
            json["modified_time_stamp_measurementoption"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_measurementoption"]),
        budgetOffset: json["budget_offset"],
        budgetOffsetType: json["budget_offset_type"],
        isAnswer: json["is_answer"],
      );

  Map<String, dynamic> toJson() => {
        "measurement_id": measurementId,
        "measurement_option_id": measurementOptionId,
        "measurement_option_description": measurementOptionDescription,
        "modified_time_stamp_measurementoption":
            modifiedTimeStampMeasurementoption?.toIso8601String(),
        "budget_offset": budgetOffset,
        "budget_offset_type": budgetOffsetType,
        "is_answer": isAnswer,
      };
}

class MeasurementPhototypesDeprecated {
  num? uploadedPictureAmount;
  bool? uploadedBlank;
  bool? uploadedAndCompleted;
  List<Photo>? photos;
  bool? attribute;
  num? folderId;
  String? folderName;
  num? folderPictureAmount;
  bool? imageRec;
  DateTime? modifiedTimeStampPhototype;

  MeasurementPhototypesDeprecated({
    this.uploadedPictureAmount,
    this.uploadedBlank,
    this.uploadedAndCompleted,
    this.photos,
    this.attribute,
    this.folderId,
    this.folderName,
    this.folderPictureAmount,
    this.imageRec,
    this.modifiedTimeStampPhototype,
  });

  factory MeasurementPhototypesDeprecated.fromRawJson(String str) =>
      MeasurementPhototypesDeprecated.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MeasurementPhototypesDeprecated.fromJson(Map<String, dynamic> json) =>
      MeasurementPhototypesDeprecated(
        uploadedPictureAmount: json["uploaded_picture_amount"],
        uploadedBlank: json["uploaded_blank"],
        uploadedAndCompleted: json["uploaded_and_completed"],
        photos: json["photos"] == null
            ? []
            : List<Photo>.from(json["photos"]!.map((x) => Photo.fromJson(x))),
        attribute: json["attribute"],
        folderId: json["folder_id"],
        folderName: json["folder_name"],
        folderPictureAmount: json["folder_picture_amount"],
        imageRec: json["image_rec"],
        modifiedTimeStampPhototype:
            json["modified_time_stamp_phototype"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototype"]),
      );

  Map<String, dynamic> toJson() => {
        "uploaded_picture_amount": uploadedPictureAmount,
        "uploaded_blank": uploadedBlank,
        "uploaded_and_completed": uploadedAndCompleted,
        "photos": photos == null
            ? []
            : List<dynamic>.from(photos!.map((x) => x.toJson())),
        "attribute": attribute,
        "folder_id": folderId,
        "folder_name": folderName,
        "folder_picture_amount": folderPictureAmount,
        "image_rec": imageRec,
        "modified_time_stamp_phototype":
            modifiedTimeStampPhototype?.toIso8601String(),
      };
}

class Photo {
  num? formId;
  num? questionId;
  num? measurementId;
  num? folderId;
  num? photoId;
  String? photoUrl;
  String? thumbnailUrl;
  String? caption;
  DateTime? modifiedTimeStampPhoto;
  bool? cannotUploadMandatory;
  bool? userDeletedPhoto;
  bool? imageRec;
  num? questionpartId;
  String? questionPartMultiId;
  num? measurementPhototypeId;
  num? combineTypeId;
  num? photoTagId;
  num? photoCombinetypeId;
  // New properties for local photo management
  String? localPath;
  bool? isEdited;

  Photo({
    this.formId,
    this.questionId,
    this.measurementId,
    this.folderId,
    this.photoId,
    this.photoUrl,
    this.thumbnailUrl,
    this.caption,
    this.modifiedTimeStampPhoto,
    this.cannotUploadMandatory,
    this.userDeletedPhoto,
    this.imageRec,
    this.questionpartId,
    this.questionPartMultiId,
    this.measurementPhototypeId,
    this.combineTypeId,
    this.photoTagId,
    this.photoCombinetypeId,
    this.localPath,
    this.isEdited,
  });

  factory Photo.fromRawJson(String str) => Photo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Photo.fromJson(Map<String, dynamic> json) => Photo(
        formId: json["form_id"],
        questionId: json["question_id"],
        measurementId: json["measurement_id"],
        folderId: json["folder_id"],
        photoId: json["photo_id"],
        photoUrl: json["photo_url"],
        thumbnailUrl: json["thumbnail_url"],
        caption: json["caption"],
        modifiedTimeStampPhoto: json["modified_time_stamp_photo"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_photo"]),
        cannotUploadMandatory: json["cannot_upload_mandatory"],
        userDeletedPhoto: json["user_deleted_photo"],
        imageRec: json["image_rec"],
        questionpartId: json["questionpart_id"],
        questionPartMultiId: json["question_part_multi_id"],
        measurementPhototypeId: json["measurement_phototype_id"],
        combineTypeId: json["combine_type_id"],
        photoTagId: json["photo_tag_id"],
        photoCombinetypeId: json["photo_combinetype_id"],
        // New properties are not included in fromJson as they are local-only
        localPath: null,
        isEdited: null,
      );

  Map<String, dynamic> toJson() => {
        "form_id": formId,
        "question_id": questionId,
        "measurement_id": measurementId,
        "folder_id": folderId,
        "photo_id": photoId,
        "photo_url": photoUrl,
        "thumbnail_url": thumbnailUrl,
        "caption": caption,
        "modified_time_stamp_photo": modifiedTimeStampPhoto?.toIso8601String(),
        "cannot_upload_mandatory": cannotUploadMandatory,
        "user_deleted_photo": userDeletedPhoto,
        "image_rec": imageRec,
        "questionpart_id": questionpartId,
        "question_part_multi_id": questionPartMultiId,
        "measurement_phototype_id": measurementPhototypeId,
        "combine_type_id": combineTypeId,
        "photo_tag_id": photoTagId,
        "photo_combinetype_id": photoCombinetypeId,
      };
}

class MeasurementValidation {
  num? measurementId;
  num? validationTypeId;
  bool? required;
  String? rangeValidation;
  String? expressionValidation;
  String? errorMessage;
  DateTime? modifiedTimeStampMeasurementvalidation;

  MeasurementValidation({
    this.measurementId,
    this.validationTypeId,
    this.required,
    this.rangeValidation,
    this.expressionValidation,
    this.errorMessage,
    this.modifiedTimeStampMeasurementvalidation,
  });

  factory MeasurementValidation.fromRawJson(String str) =>
      MeasurementValidation.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MeasurementValidation.fromJson(Map<String, dynamic> json) =>
      MeasurementValidation(
        measurementId: json["measurement_id"],
        validationTypeId: json["validation_type_id"],
        required: json["required"],
        rangeValidation: json["range_validation"],
        expressionValidation: json["expression_validation"],
        errorMessage: json["error_message"],
        modifiedTimeStampMeasurementvalidation:
            json["modified_time_stamp_measurementvalidation"] == null
                ? null
                : DateTime.parse(
                    json["modified_time_stamp_measurementvalidation"]),
      );

  Map<String, dynamic> toJson() => {
        "measurement_id": measurementId,
        "validation_type_id": validationTypeId,
        "required": required,
        "range_validation": rangeValidation,
        "expression_validation": expressionValidation,
        "error_message": errorMessage,
        "modified_time_stamp_measurementvalidation":
            modifiedTimeStampMeasurementvalidation?.toIso8601String(),
      };
}

class PhotoTagsT {
  num? questionpartId;
  num? measurementId;
  bool? isMandatory;
  num? photoResPerc;
  bool? liveImagesOnly;
  num? photoTagId;
  String? photoTag;
  num? numberOfPhotos;
  num? measurementPhototypeId;
  bool? imageRec;
  List<Photo>? userPhotos;

  PhotoTagsT({
    this.questionpartId,
    this.measurementId,
    this.isMandatory,
    this.photoResPerc,
    this.liveImagesOnly,
    this.photoTagId,
    this.photoTag,
    this.numberOfPhotos,
    this.measurementPhototypeId,
    this.imageRec,
    this.userPhotos,
  });

  factory PhotoTagsT.fromRawJson(String str) =>
      PhotoTagsT.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PhotoTagsT.fromJson(Map<String, dynamic> json) => PhotoTagsT(
        questionpartId: json["questionpart_id"],
        measurementId: json["measurement_id"],
        isMandatory: json["is_mandatory"],
        photoResPerc: json["PhotoResPerc"],
        liveImagesOnly: json["live_images_only"],
        photoTagId: json["photo_tag_id"],
        photoTag: json["photo_tag"],
        numberOfPhotos: json["number_of_photos"],
        measurementPhototypeId: json["measurement_phototype_id"],
        imageRec: json["image_rec"],
        userPhotos: json["user_photos"] == null
            ? []
            : List<Photo>.from(
                json["user_photos"]!.map((x) => Photo.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "questionpart_id": questionpartId,
        "measurement_id": measurementId,
        "is_mandatory": isMandatory,
        "PhotoResPerc": photoResPerc,
        "live_images_only": liveImagesOnly,
        "photo_tag_id": photoTagId,
        "photo_tag": photoTag,
        "number_of_photos": numberOfPhotos,
        "measurement_phototype_id": measurementPhototypeId,
        "image_rec": imageRec,
        "user_photos": userPhotos == null
            ? []
            : List<dynamic>.from(userPhotos!.map((x) => x.toJson())),
      };
}

class QuestionCondition {
  num? measurementId;
  num? measurementOptionId;
  num? actionQuestionId;
  String? action;
  DateTime? modifiedTimeStampQuestioncondition;

  QuestionCondition({
    this.measurementId,
    this.measurementOptionId,
    this.actionQuestionId,
    this.action,
    this.modifiedTimeStampQuestioncondition,
  });

  factory QuestionCondition.fromRawJson(String str) =>
      QuestionCondition.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory QuestionCondition.fromJson(Map<String, dynamic> json) =>
      QuestionCondition(
        measurementId: json["measurement_id"],
        measurementOptionId: json["measurement_option_id"],
        actionQuestionId: json["action_question_id"],
        action: json["action"],
        modifiedTimeStampQuestioncondition:
            json["modified_time_stamp_questioncondition"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_questioncondition"]),
      );

  Map<String, dynamic> toJson() => {
        "measurement_id": measurementId,
        "measurement_option_id": measurementOptionId,
        "action_question_id": actionQuestionId,
        "action": action,
        "modified_time_stamp_questioncondition":
            modifiedTimeStampQuestioncondition?.toIso8601String(),
      };
}

class QuestionPart {
  num? projectid;
  num? questionpartId;
  String? questionpartDescription;
  String? price;
  DateTime? modifiedTimeStampQuestionpart;
  bool? targetByCycle;
  bool? targetByGroup;
  bool? targetByCompany;
  bool? targetByRegion;
  bool? targetByBudget;
  bool? osaForm;
  num? companyId;
  String? itemImage;
  num? targeted;
  String? questionpartMultiId;

  QuestionPart({
    this.projectid,
    this.questionpartId,
    this.questionpartDescription,
    this.price,
    this.modifiedTimeStampQuestionpart,
    this.targetByCycle,
    this.targetByGroup,
    this.targetByCompany,
    this.targetByRegion,
    this.targetByBudget,
    this.osaForm,
    this.companyId,
    this.itemImage,
    this.targeted,
    this.questionpartMultiId,
  });

  factory QuestionPart.fromRawJson(String str) =>
      QuestionPart.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory QuestionPart.fromJson(Map<String, dynamic> json) => QuestionPart(
        projectid: json["projectid"],
        questionpartId: json["questionpart_id"],
        questionpartDescription: json["questionpart_description"],
        price: json["price"],
        modifiedTimeStampQuestionpart:
            json["modified_time_stamp_questionpart"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_questionpart"]),
        targetByCycle: json["targetByCycle"],
        targetByGroup: json["targetByGroup"],
        targetByCompany: json["targetByCompany"],
        targetByRegion: json["targetByRegion"],
        targetByBudget: json["targetByBudget"],
        osaForm: json["osaForm"],
        companyId: json["company_id"],
        itemImage: json["item_image"],
        targeted: json["targeted"],
      );

  Map<String, dynamic> toJson() => {
        "projectid": projectid,
        "questionpart_id": questionpartId,
        "questionpart_description": questionpartDescription,
        "price": price,
        "modified_time_stamp_questionpart":
            modifiedTimeStampQuestionpart?.toIso8601String(),
        "targetByCycle": targetByCycle,
        "targetByGroup": targetByGroup,
        "targetByCompany": targetByCompany,
        "targetByRegion": targetByRegion,
        "targetByBudget": targetByBudget,
        "osaForm": osaForm,
        "company_id": companyId,
        "item_image": itemImage,
        "targeted": targeted,
      };
}

class PhotoFolder {
  List<Photo>? photos;
  bool? attribute;
  num? folderId;
  String? folderName;
  num? folderPictureAmount;
  bool? imageRec;
  DateTime? modifiedTimeStampPhototype;

  PhotoFolder({
    this.photos,
    this.attribute,
    this.folderId,
    this.folderName,
    this.folderPictureAmount,
    this.imageRec,
    this.modifiedTimeStampPhototype,
  });

  factory PhotoFolder.fromRawJson(String str) =>
      PhotoFolder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PhotoFolder.fromJson(Map<String, dynamic> json) => PhotoFolder(
        photos: json["photos"] == null
            ? []
            : List<Photo>.from(json["photos"]!.map((x) => Photo.fromJson(x))),
        attribute: json["attribute"],
        folderId: json["folder_id"],
        folderName: json["folder_name"],
        folderPictureAmount: json["folder_picture_amount"],
        imageRec: json["image_rec"],
        modifiedTimeStampPhototype:
            json["modified_time_stamp_phototype"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototype"]),
      );

  Map<String, dynamic> toJson() => {
        "photos": photos == null
            ? []
            : List<dynamic>.from(photos!.map((x) => x.toJson())),
        "attribute": attribute,
        "folder_id": folderId,
        "folder_name": folderName,
        "folder_picture_amount": folderPictureAmount,
        "image_rec": imageRec,
        "modified_time_stamp_phototype":
            modifiedTimeStampPhototype?.toIso8601String(),
      };
}

class PosItem {
  String? itemName;
  num? itemAmount;
  String? photoUrl;

  PosItem({
    this.itemName,
    this.itemAmount,
    this.photoUrl,
  });

  factory PosItem.fromRawJson(String str) => PosItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PosItem.fromJson(Map<String, dynamic> json) => PosItem(
        itemName: json["item_name"],
        itemAmount: json["item_amount"],
        photoUrl: json["photo_url"],
      );

  Map<String, dynamic> toJson() => {
        "item_name": itemName,
        "item_amount": itemAmount,
        "photo_url": photoUrl,
      };
}

class SignatureFolder {
  List<Signature>? signatures;
  bool? attribute;
  num? folderId;
  String? folderName;
  DateTime? modifiedTimeStampSignaturetype;

  SignatureFolder({
    this.signatures,
    this.attribute,
    this.folderId,
    this.folderName,
    this.modifiedTimeStampSignaturetype,
  });

  factory SignatureFolder.fromRawJson(String str) =>
      SignatureFolder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SignatureFolder.fromJson(Map<String, dynamic> json) =>
      SignatureFolder(
        signatures: json["signatures"] == null
            ? []
            : List<Signature>.from(
                json["signatures"]!.map((x) => Signature.fromJson(x))),
        attribute: json["attribute"],
        folderId: json["folder_id"],
        folderName: json["folder_name"],
        modifiedTimeStampSignaturetype:
            json["modified_time_stamp_signaturetype"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signaturetype"]),
      );

  Map<String, dynamic> toJson() => {
        "signatures": signatures == null
            ? []
            : List<dynamic>.from(signatures!.map((x) => x.toJson())),
        "attribute": attribute,
        "folder_id": folderId,
        "folder_name": folderName,
        "modified_time_stamp_signaturetype":
            modifiedTimeStampSignaturetype?.toIso8601String(),
      };
}

class Signature {
  num? signatureId;
  num? formId;
  num? questionId;
  String? signatureUrl;
  String? thumbnailUrl;
  String? signedBy;
  DateTime? modifiedTimeStampSignature;
  bool? userDeletedSignature;
  bool? cannotUploadMandatory;
  // New properties for local signature management
  String? localPath;
  bool? isEdited;

  Signature({
    this.signatureId,
    this.formId,
    this.questionId,
    this.signatureUrl,
    this.thumbnailUrl,
    this.signedBy,
    this.modifiedTimeStampSignature,
    this.userDeletedSignature,
    this.cannotUploadMandatory,
    this.localPath,
    this.isEdited,
  });

  factory Signature.fromRawJson(String str) =>
      Signature.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Signature.fromJson(Map<String, dynamic> json) => Signature(
        signatureId: json["signature_id"],
        formId: json["form_id"],
        questionId: json["question_id"],
        signatureUrl: json["signature_url"],
        thumbnailUrl: json["thumbnail_url"],
        signedBy: json["signed_by"],
        modifiedTimeStampSignature:
            json["modified_time_stamp_signature"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signature"]),
        userDeletedSignature: json["user_deleted_signature"],
        cannotUploadMandatory: json["cannot_upload_mandatory"],
        // New properties are not included in fromJson as they are local-only
        localPath: null,
        isEdited: null,
      );

  Map<String, dynamic> toJson() => {
        "signature_id": signatureId,
        "form_id": formId,
        "question_id": questionId,
        "signature_url": signatureUrl,
        "thumbnail_url": thumbnailUrl,
        "signed_by": signedBy,
        "modified_time_stamp_signature":
            modifiedTimeStampSignature?.toIso8601String(),
        "user_deleted_signature": userDeletedSignature,
        "cannot_upload_mandatory": cannotUploadMandatory,
      };
}

class Stocktake {
  num? taskId;
  num? itemId;
  String? itemCode;
  String? itemName;
  String? itemGroup;
  String? imageUrl;
  String? itemLocation;
  num? itemQty;

  Stocktake({
    this.taskId,
    this.itemId,
    this.itemCode,
    this.itemName,
    this.itemGroup,
    this.imageUrl,
    this.itemLocation,
    this.itemQty,
  });

  factory Stocktake.fromRawJson(String str) =>
      Stocktake.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Stocktake.fromJson(Map<String, dynamic> json) => Stocktake(
        taskId: json["task_id"],
        itemId: json["item_id"],
        itemCode: json["item_code"],
        itemName: json["item_name"],
        itemGroup: json["item_group"],
        imageUrl: json["image_URL"],
        itemLocation: json["item_location"],
        itemQty: json["item_qty"],
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "item_id": itemId,
        "item_code": itemCode,
        "item_name": itemName,
        "item_group": itemGroup,
        "image_URL": imageUrl,
        "item_location": itemLocation,
        "item_qty": itemQty,
      };
}

class Taskalert {
  num? messageId;
  num? schedulepeopleid;
  String? subject;
  String? message;

  Taskalert({
    this.messageId,
    this.schedulepeopleid,
    this.subject,
    this.message,
  });

  factory Taskalert.fromRawJson(String str) =>
      Taskalert.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Taskalert.fromJson(Map<String, dynamic> json) => Taskalert(
        messageId: json["message_id"],
        schedulepeopleid: json["schedulepeopleid"],
        subject: json["subject"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "message_id": messageId,
        "schedulepeopleid": schedulepeopleid,
        "subject": subject,
        "message": message,
      };
}

class Taskmember {
  String? fullname;
  num? teamLead;
  String? email;
  num? scheduleId;
  num? taskId;

  Taskmember({
    this.fullname,
    this.teamLead,
    this.email,
    this.scheduleId,
    this.taskId,
  });

  factory Taskmember.fromRawJson(String str) =>
      Taskmember.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Taskmember.fromJson(Map<String, dynamic> json) => Taskmember(
        fullname: json["fullname"],
        teamLead: json["team_lead"],
        email: json["email"],
        scheduleId: json["schedule_id"],
        taskId: json["task_id"],
      );

  Map<String, dynamic> toJson() => {
        "fullname": fullname,
        "team_lead": teamLead,
        "email": email,
        "schedule_id": scheduleId,
        "task_id": taskId,
      };
}

class TaskMembersHelper {
  int? taskId;
  List<Taskmember>? taskMembers;

  TaskMembersHelper({
    this.taskId,
    this.taskMembers,
  });

  factory TaskMembersHelper.fromJson(Map<String, dynamic> json) =>
      TaskMembersHelper(
        taskId: json["task_id"],
        taskMembers: json["task_members"] == null
            ? []
            : List<Taskmember>.from(
                json["task_members"]!.map((x) => Taskmember.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "task_members": taskMembers == null
            ? []
            : List<dynamic>.from(taskMembers!.map((x) => x.toJson())),
      };
}

class ResumePauseItem {
  String? resumeOrderID;
  DateTime? resumeDate;
  DateTime? pauseDate;

  ResumePauseItem({
    this.resumeOrderID,
    this.resumeDate,
    this.pauseDate,
  });

  factory ResumePauseItem.fromJson(Map<String, dynamic> json) {
    return ResumePauseItem(
      resumeOrderID: json['resumeOrderID'] as String?,
      resumeDate: json['resumeDate'] != null
          ? DateTime.parse(json['resumeDate'] as String)
          : null,
      pauseDate: json['pauseDate'] != null
          ? DateTime.parse(json['pauseDate'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'resumeOrderID': resumeOrderID,
      'resumeDate': resumeDate?.toIso8601String(),
      'pauseDate': pauseDate?.toIso8601String(),
    };
  }

  static Map<String, dynamic> encode(ResumePauseItem model) {
    return model.toJson();
  }

  static ResumePauseItem decode(Map<String, dynamic> json) {
    return ResumePauseItem.fromJson(json);
  }
}
